import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import Fastify from 'fastify';
import { setupTestDatabase, prisma, createTestUser } from '../../setup/test-db-setup';
import authRoutes from '../../../src/backend/routes/auth';
import { userWebhooksRoutes } from '../../../src/backend/routes/user-webhooks.routes';
import { domainsRoutes } from '../../../src/backend/routes/domains.routes';
import { userAliasRoutes } from '../../../src/backend/routes/user-aliases.routes';
import cookie from '@fastify/cookie';
import formbody from '@fastify/formbody';
import { env } from '../../../src/backend/config/env';

describe('Authentication Flow Integration Tests', () => {
  let app: FastifyInstance;

  setupTestDatabase();

  beforeAll(async () => {
    // Create a test-specific Fastify instance
    app = Fastify({
      logger: false // Disable logging in tests
    });

    // Register necessary plugins
    await app.register(formbody);
    await app.register(cookie, {
      secret: env.ADMIN_JWT_SECRET,
      parseOptions: {}
    } as any);

    // Register routes
    await app.register(authRoutes, { prefix: '/' });
    await app.register(userWebhooksRoutes, { prefix: '/api' });
    await app.register(domainsRoutes, { prefix: '/api' });
    await app.register(userAliasRoutes, { prefix: '/api' });

    await app.ready();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('Login Flow', () => {
    test('should set user_token cookie on successful login', async () => {
      // Create test user
      const testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'password123'
      });

      const response = await app.inject({
        method: 'POST',
        url: '/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      });

      expect(response.statusCode).toBe(200);
      
      // Check that user_token cookie is set
      const cookies = response.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      
      expect(userTokenCookie).toBeDefined();
      expect(userTokenCookie?.value).toBeTruthy();
      expect(userTokenCookie?.httpOnly).toBe(true);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody.message).toBe('Login successful');
      expect(responseBody.user.email).toBe('<EMAIL>');
    });

    test('should reject login with invalid credentials', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/login',
        payload: {
          email: '<EMAIL>',
          password: 'wrongpassword'
        }
      });

      expect(response.statusCode).toBe(401);
      
      // Should not set any cookies
      const cookies = response.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      expect(userTokenCookie).toBeUndefined();
    });
  });

  describe('Register Flow', () => {
    test('should set user_token cookie on successful registration', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/register',
        payload: {
          email: '<EMAIL>',
          password: 'password123',
          confirmPassword: 'password123'
        }
      });

      expect(response.statusCode).toBe(201);
      
      // Check that user_token cookie is set
      const cookies = response.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      
      expect(userTokenCookie).toBeDefined();
      expect(userTokenCookie?.value).toBeTruthy();
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody.message).toBe('Registration successful');
      expect(responseBody.user.email).toBe('<EMAIL>');
    });

    test('should reject registration with mismatched passwords', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/register',
        payload: {
          email: '<EMAIL>',
          password: 'password123',
          confirmPassword: 'different123'
        }
      });

      expect(response.statusCode).toBe(400);
      
      // Should not set any cookies
      const cookies = response.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      expect(userTokenCookie).toBeUndefined();
    });
  });

  describe('Protected API Endpoints', () => {
    let userToken: string;
    let testUserId: string;

    beforeAll(async () => {
      // Create test user and get token
      const testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'password123'
      });
      testUserId = testUser.id;

      const loginResponse = await app.inject({
        method: 'POST',
        url: '/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      });

      const cookies = loginResponse.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      userToken = userTokenCookie?.value || '';
    });

    test('should allow access to /api/domains with valid cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/domains',
        cookies: {
          user_token: userToken
        }
      });

      expect(response.statusCode).toBe(200);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toHaveProperty('domains');
      expect(Array.isArray(responseBody.domains)).toBe(true);
    });

    test('should allow access to /api/webhooks with valid cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/webhooks',
        cookies: {
          user_token: userToken
        }
      });

      expect(response.statusCode).toBe(200);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toHaveProperty('webhooks');
      expect(Array.isArray(responseBody.webhooks)).toBe(true);
    });

    test('should allow access to /api/aliases with valid cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/aliases',
        cookies: {
          user_token: userToken
        }
      });

      expect(response.statusCode).toBe(200);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toHaveProperty('aliases');
      expect(Array.isArray(responseBody.aliases)).toBe(true);
    });

    test('should deny access to /api/domains without cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/domains'
      });

      expect(response.statusCode).toBe(401);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Unauthorized: No token provided');
    });

    test('should deny access to /api/webhooks without cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/webhooks'
      });

      expect(response.statusCode).toBe(401);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Unauthorized: No token provided');
    });

    test('should deny access to /api/aliases without cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/aliases'
      });

      expect(response.statusCode).toBe(401);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Unauthorized: No token provided');
    });

    test('should deny access with invalid cookie', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/domains',
        cookies: {
          user_token: 'invalid-token-123'
        }
      });

      expect(response.statusCode).toBe(401);
      
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Unauthorized: Invalid token');
    });
  });

  describe('Logout Flow', () => {
    test('should clear user_token cookie on logout', async () => {
      // First login to get a token
      const testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'password123'
      });

      const loginResponse = await app.inject({
        method: 'POST',
        url: '/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      });

      const cookies = loginResponse.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      const userToken = userTokenCookie?.value || '';

      // Now logout
      const logoutResponse = await app.inject({
        method: 'POST',
        url: '/logout',
        cookies: {
          user_token: userToken
        }
      });

      expect(logoutResponse.statusCode).toBe(200);
      
      // Check that user_token cookie is cleared
      const logoutCookies = logoutResponse.cookies;
      const clearedCookie = logoutCookies.find(cookie => cookie.name === 'user_token');
      
      expect(clearedCookie).toBeDefined();
      expect(clearedCookie?.value).toBe(''); // Should be empty
      expect(clearedCookie?.expires).toBeDefined(); // Should have past expiry date
    });

    test('should deny API access after logout', async () => {
      // Create user and login
      const testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'password123'
      });

      const loginResponse = await app.inject({
        method: 'POST',
        url: '/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      });

      const cookies = loginResponse.cookies;
      const userTokenCookie = cookies.find(cookie => cookie.name === 'user_token');
      const userToken = userTokenCookie?.value || '';

      // Verify API access works before logout
      const beforeLogoutResponse = await app.inject({
        method: 'GET',
        url: '/api/domains',
        cookies: {
          user_token: userToken
        }
      });
      expect(beforeLogoutResponse.statusCode).toBe(200);

      // Logout
      await app.inject({
        method: 'POST',
        url: '/logout',
        cookies: {
          user_token: userToken
        }
      });

      // Try to access API with the same token (should fail)
      const afterLogoutResponse = await app.inject({
        method: 'GET',
        url: '/api/domains',
        cookies: {
          user_token: userToken
        }
      });

      expect(afterLogoutResponse.statusCode).toBe(401);
    });
  });
});
