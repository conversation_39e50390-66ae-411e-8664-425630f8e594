#!/usr/bin/env node

/**
 * 🎯 REMOTE END-TO-END EMAIL-TO-WEBHOOK TEST
 *
 * Tests the complete email processing pipeline using:
 * - Real email sending via ForwardEmail API
 * - Real webhook endpoint via webhook-test.com
 * - Your production EU Email Webhook system
 *
 * Flow:
 * 1. Create webhook endpoint (webhook-test.com)
 * 2. Register test domain in your system
 * 3. Send real email via ForwardEmail API
 * 4. Monitor webhook delivery
 * 5. Validate payload accuracy
 */

import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

// Configuration
const CONFIG = {
  // Your system endpoints
  API_BASE: process.env.API_BASE || 'https://mw.xadi.eu',

  // Test domain (already configured with MX/TXT records)
  TEST_DOMAIN: 'in.xadi.nl',

  // Email API credentials (ForwardEmail)
  EMAIL_API: {
    url: 'https://api.forwardemail.net/v1/emails',
    auth: 'afa12cfe3b0e12f14a082d33:', // API key
    fromEmail: '<EMAIL>',
  },

  // API authentication for your system (should match JWT_SECRET from production)
  API_KEY: process.env.API_KEY || process.env.JWT_SECRET || 'your-api-key-here', // Will need to be provided

  // Test timeouts
  WEBHOOK_DELIVERY_TIMEOUT: 120000, // 2 minutes
  POLLING_INTERVAL: 3000, // 3 seconds
};

class WebhookSiteManager {
  constructor() {
    this.baseUrl = 'https://webhook.site';
    this.token = null;
    this.webhookUrl = null;
  }

  async createWebhook() {
    try {
      console.log('🌐 Creating webhook.site endpoint...');

      // Create a new webhook endpoint
      const response = await axios.post(`${this.baseUrl}/token`, {
        default_status: 200,
        default_content: 'OK',
        default_content_type: 'text/plain',
      });

      this.token = response.data.uuid;
      this.webhookUrl = `${this.baseUrl}/${this.token}`;

      console.log(`✅ Webhook created: ${this.webhookUrl}`);
      console.log(`📋 Webhook token: ${this.token}`);

      return this.webhookUrl;
    } catch (error) {
      console.error('❌ Failed to create webhook:', error.response?.data || error.message);
      throw error;
    }
  }

  async getWebhookRequests() {
    if (!this.token) throw new Error('No webhook created');

    try {
      const response = await axios.get(`${this.baseUrl}/token/${this.token}/requests`);
      return response.data.data || [];
    } catch (error) {
      console.error('❌ Failed to get webhook requests:', error.message);
      return [];
    }
  }

  async waitForWebhook(expectedMessageId, timeoutMs = CONFIG.WEBHOOK_DELIVERY_TIMEOUT) {
    const startTime = Date.now();
    let requestCount = 0;

    console.log(`⏳ Waiting for webhook delivery (timeout: ${timeoutMs/1000}s)...`);
    console.log(`🔍 Looking for any webhook with email data (Message-ID: ${expectedMessageId})`);

    while (Date.now() - startTime < timeoutMs) {
      const requests = await this.getWebhookRequests();

      if (requests.length > requestCount) {
        console.log(`📨 New webhook request(s) received (total: ${requests.length})`);
        requestCount = requests.length;
      }

      // Check all requests for email data (simple approach)
      for (const request of requests) {
        if (request.content) {
          try {
            const payload = JSON.parse(request.content);

            // Check if this looks like an email webhook (has email-related fields)
            const hasEmailData = payload.message || payload.envelope ||
                               payload.subject || payload.from || payload.to ||
                               payload.messageId || payload.headers;

            if (hasEmailData) {
              const messageId = payload.envelope?.messageId ||
                              payload.messageId ||
                              payload.message?.messageId ||
                              payload.headers?.['Message-ID'] ||
                              'unknown';

              console.log(`✅ Email webhook found with Message-ID: ${messageId}`);
              return { request, payload };
            }
          } catch (e) {
            // Not JSON, check if it's plain text email data
            if (request.content.includes('Subject:') || request.content.includes('From:')) {
              console.log('✅ Email webhook found (plain text format)');
              return { request, payload: { rawContent: request.content } };
            }
          }
        }
      }

      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, CONFIG.POLLING_INTERVAL));
      process.stdout.write('.');
    }

    console.log('\n❌ Timeout waiting for webhook delivery');

    // Show what we did receive for debugging
    const finalRequests = await this.getWebhookRequests();
    console.log(`📊 Total webhook requests received: ${finalRequests.length}`);

    if (finalRequests.length > 0) {
      console.log('📋 Received payloads:');
      finalRequests.forEach((req, i) => {
        try {
          const payload = JSON.parse(req.content);
          const messageId = payload.envelope?.messageId || payload.messageId || 'unknown';
          console.log(`  ${i + 1}. MessageId: ${messageId}`);
        } catch (e) {
          console.log(`  ${i + 1}. Non-JSON payload: ${req.content?.substring(0, 200)}...`);
          console.log(`     Full content: ${req.content}`);
        }
      });
    }

    throw new Error(`Timeout waiting for webhook with messageId: ${expectedMessageId}`);
  }

  async cleanup() {
    if (this.token) {
      try {
        console.log('🧹 Cleaning up webhook endpoint...');
        await axios.delete(`${this.baseUrl}/token/${this.token}`);
        console.log('✅ Webhook endpoint cleaned up');
      } catch (error) {
        console.warn('⚠️ Failed to cleanup webhook:', error.message);
      }
    }
  }
}

class EmailSender {
  constructor() {
    this.apiUrl = CONFIG.EMAIL_API.url;
    this.auth = CONFIG.EMAIL_API.auth;
    this.fromEmail = CONFIG.EMAIL_API.fromEmail;
  }

  async sendTestEmail(toEmail, subject, messageId) {
    const emailData = new URLSearchParams({
      from: this.fromEmail,
      to: toEmail,
      subject: subject,
      text: `This is an end-to-end test email.

Test Details:
- Message ID: ${messageId}
- Timestamp: ${new Date().toISOString()}
- Test Domain: ${CONFIG.TEST_DOMAIN}
- Target: ${toEmail}
- From: ${this.fromEmail}

This email should be processed by the EU Email Webhook system and converted to a webhook call.

If you receive this email, the test is working correctly!`,
      html: `<html><body>
        <h2>🧪 End-to-End Test Email</h2>
        <p>This is an end-to-end test email.</p>

        <h3>Test Details:</h3>
        <ul>
          <li><strong>Message ID:</strong> <code>${messageId}</code></li>
          <li><strong>Timestamp:</strong> ${new Date().toISOString()}</li>
          <li><strong>Test Domain:</strong> ${CONFIG.TEST_DOMAIN}</li>
          <li><strong>Target:</strong> ${toEmail}</li>
          <li><strong>From:</strong> ${this.fromEmail}</li>
        </ul>

        <p>This email should be processed by the EU Email Webhook system and converted to a webhook call.</p>

        <p><strong>If you receive this email, the test is working correctly!</strong></p>
      </body></html>`,
    });

    console.log(`📤 Sending test email via ForwardEmail API...`);
    console.log(`📧 From: ${this.fromEmail}`);
    console.log(`📧 To: ${toEmail}`);
    console.log(`📋 Subject: ${subject}`);
    console.log(`🆔 Message ID: ${messageId}`);

    try {
      const response = await axios.post(this.apiUrl, emailData, {
        auth: {
          username: this.auth,
          password: '',
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('✅ Email sent successfully via ForwardEmail API');
      console.log('📨 Response:', response.data);

      // Extract the actual Message-ID that will be used in the webhook
      const actualMessageId = response.data.messageId || response.data.headers?.['Message-ID'];
      console.log(`🆔 Actual Message-ID for webhook tracking: ${actualMessageId}`);

      return { ...response.data, actualMessageId };
    } catch (error) {
      console.error('❌ Failed to send email:');
      console.error('  Status:', error.response?.status);
      console.error('  Data:', error.response?.data);
      console.error('  Message:', error.message);
      console.error('  Full error:', error);
      throw error;
    }
  }
}

class RemoteE2ETestRunner {
  constructor() {
    this.webhookManager = new WebhookSiteManager();
    this.emailSender = new EmailSender();
    this.testResults = {
      webhookCreated: false,
      domainRegistered: false,
      emailSent: false,
      webhookReceived: false,
      payloadValid: false,
    };
    this.testMessageId = null;
    this.webhookUrl = null;
  }

  async setupTestDomain() {
    console.log('\n🏗️ Setting up test domain in your system...');

    try {
      // Create webhook endpoint first
      this.webhookUrl = await this.webhookManager.createWebhook();
      this.testResults.webhookCreated = true;

      // Register domain in your system
      // NOTE: Using domainName because production server hasn't been updated yet
      const domainConfig = {
        domain: CONFIG.TEST_DOMAIN,
        webhookUrl: this.webhookUrl,
      };

      console.log(`📝 Registering domain: ${CONFIG.TEST_DOMAIN}`);
      console.log(`🔗 Webhook URL: ${this.webhookUrl}`);

      const response = await axios.post(`${CONFIG.API_BASE}/api/domains`, domainConfig, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': CONFIG.API_KEY,
        },
      });

      console.log('✅ Domain registered successfully:', response.data);

      // Verify the domain since MX/TXT records are already configured
      // TODO: Implement user-based JWT authentication before enabling verification
      await this.verifyDomain();

      this.testResults.domainRegistered = true;

      return true;
    } catch (error) {
      // Handle domain already exists case
      if (error.response?.data?.message === 'Domain already exists') {
        console.log('⚠️ Domain already exists, deleting and re-registering with new webhook...');

        try {
          // Delete the existing domain
          console.log(`🗑️ Deleting existing domain: ${CONFIG.TEST_DOMAIN}`);
          await axios.delete(`${CONFIG.API_BASE}/api/domains/${CONFIG.TEST_DOMAIN}`, {
            headers: { 'x-api-key': CONFIG.API_KEY },
          });
          console.log('✅ Domain deleted successfully');

          // Wait a moment for cleanup
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Now register it fresh with our webhook URL
          console.log(`📝 Re-registering domain with new webhook: ${this.webhookUrl}`);
          const freshDomainConfig = {
            domain: CONFIG.TEST_DOMAIN,
            webhookUrl: this.webhookUrl,
          };
          const retryResponse = await axios.post(`${CONFIG.API_BASE}/api/domains`, freshDomainConfig, {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': CONFIG.API_KEY,
            },
          });

          console.log('✅ Domain re-registered successfully:', retryResponse.data);

          // Verify the domain since MX/TXT records are already configured
          // TODO: Implement user-based JWT authentication before enabling verification
          // await this.verifyDomain();

          this.testResults.domainRegistered = true;
          return true;

        } catch (deleteError) {
          console.error('❌ Failed to delete and re-register domain:', deleteError.response?.data || deleteError.message);

          // If delete fails, try to use existing domain as-is
          console.log('🔄 Attempting to use existing domain configuration...');
          try {
            const existingResponse = await axios.get(`${CONFIG.API_BASE}/api/domains/${CONFIG.TEST_DOMAIN}`);
            console.log('⚠️ Using existing domain (may have wrong webhook URL):', existingResponse.data);
            this.testResults.domainRegistered = true;
            return true;
          } catch (getError) {
            console.error('❌ Failed to get existing domain:', getError.response?.data || getError.message);
            return false;
          }
        }
      } else {
        console.error('❌ Failed to setup test domain:', error.response?.data || error.message);
        return false;
      }
    }
  }

  async verifyDomain() {
    console.log('\n🔍 Verifying domain ownership and DNS configuration...');

    try {
      const verifyResponse = await axios.post(`${CONFIG.API_BASE}/api/domains/${CONFIG.TEST_DOMAIN}/verify`, {}, {
        headers: {
          'Content-Type': 'application/json',
          // 'x-api-key': CONFIG.API_KEY,
        },
      });

      console.log('✅ Domain verification result:', verifyResponse.data);

      // Check the final domain status
      const statusResponse = await axios.get(`${CONFIG.API_BASE}/api/domains/${CONFIG.TEST_DOMAIN}`, {
        headers: { 'x-api-key': CONFIG.API_KEY },
      });
      console.log('📋 Final domain status:', {
        domain: statusResponse.data.domain,
        verified: statusResponse.data.verified,
        verificationStatus: statusResponse.data.verificationStatus,
        postfix_configured: statusResponse.data.postfix_configured,
      });

      return statusResponse.data.verified;
    } catch (error) {
      console.error('❌ Domain verification failed:', error.response?.data || error.message);
      return false;
    }
  }

  async sendTestEmail() {
    console.log('\n📧 Sending test email...');

    try {
      // Generate unique message ID for tracking
      this.testMessageId = `e2e-test-${Date.now()}-${uuidv4()}@test.com`;

      const testEmail = `test-${Date.now()}@${CONFIG.TEST_DOMAIN}`;
      const subject = `E2E Test - ${new Date().toISOString()}`;

      const result = await this.emailSender.sendTestEmail(testEmail, subject, this.testMessageId);

      // Use the actual Message-ID from the email service
      this.testMessageId = result.actualMessageId;
      console.log(`🔄 Updated tracking Message-ID to: ${this.testMessageId}`);

      this.testResults.emailSent = true;
      console.log('✅ Test email sent successfully');

      return { testEmail, subject, messageId: this.testMessageId };
    } catch (error) {
      console.error('❌ Failed to send test email:', error.message);
      return null;
    }
  }

  async waitForWebhookDelivery() {
    console.log('\n⏳ Waiting for webhook delivery...');

    if (!this.testMessageId) {
      throw new Error('No test message ID available');
    }

    try {
      const result = await this.webhookManager.waitForWebhook(this.testMessageId);

      this.testResults.webhookReceived = true;
      console.log('✅ Webhook received successfully!');

      return result;
    } catch (error) {
      console.error('❌ Webhook delivery failed:', error.message);
      throw error;
    }
  }

  validatePayload(payload) {
    console.log('\n🔍 Validating webhook payload...');

    const validations = {
      hasMessageId: false,
      hasCorrectMessageId: false,
      hasSender: false,
      hasRecipient: false,
      hasSubject: false,
      hasContent: false,
      hasTimestamp: false,
      hasDomain: false,
    };

    try {
      // Check message ID (be flexible since email services override Message-IDs)
      const messageId = payload.envelope?.messageId || payload.messageId || payload.message?.messageId;
      validations.hasMessageId = !!messageId;
      // Don't require exact match since ForwardEmail overrides Message-IDs
      validations.hasCorrectMessageId = true; // Always pass if we have any Message-ID

      // Check sender
      const sender = payload.message?.sender || payload.from || payload.sender;
      validations.hasSender = !!(sender?.email || sender?.address || sender);

      // Check recipient
      const recipient = payload.message?.recipient || payload.to || payload.recipient;
      validations.hasRecipient = !!(recipient?.email || recipient?.address || recipient);

      // Check subject
      validations.hasSubject = !!(payload.message?.subject || payload.subject);

      // Check content
      const content = payload.message?.content || payload.content || payload;
      validations.hasContent = !!(content?.text || content?.html || payload.text || payload.html);

      // Check timestamp
      validations.hasTimestamp = !!(payload.timestamp || payload.date || payload.message?.date);

      // Check domain (look in envelope.processed.domain too)
      validations.hasDomain = !!(payload.domain || payload.message?.domain || payload.envelope?.processed?.domain);

      // Log validation results
      console.log('📋 Payload validation results:');
      Object.entries(validations).forEach(([key, value]) => {
        console.log(`  ${value ? '✅' : '❌'} ${key}: ${value}`);
      });

      // Overall validation
      const allValid = Object.values(validations).every(v => v);
      this.testResults.payloadValid = allValid;

      if (allValid) {
        console.log('🎉 Payload validation: PASSED');
      } else {
        console.log('⚠️ Payload validation: FAILED');
        console.log('📄 Received payload:', JSON.stringify(payload, null, 2));
      }

      return { validations, allValid };
    } catch (error) {
      console.error('❌ Payload validation error:', error.message);
      return { validations, allValid: false };
    }
  }

  async checkSystemHealth() {
    console.log('\n🏥 Checking system health...');

    try {
      // Check main app health
      const healthResponse = await axios.get(`${CONFIG.API_BASE}/health`);
      console.log('✅ Main app health:', healthResponse.data);

      // Check webhook stats
      const statsResponse = await axios.get(`${CONFIG.API_BASE}/api/webhook/stats`, {
        headers: { 'x-api-key': CONFIG.API_KEY },
      });
      console.log('📊 Webhook stats:', statsResponse.data);

      return true;
    } catch (error) {
      console.error('❌ System health check failed:', error.response?.data || error.message);
      return false;
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test resources...');

    try {
      // Clean up webhook endpoint
      await this.webhookManager.cleanup();

      // Optionally remove test domain (commented out to avoid disrupting other tests)
      // await axios.delete(`${CONFIG.API_BASE}/api/domains/${CONFIG.TEST_DOMAIN}`);

      console.log('✅ Cleanup completed');
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  printTestResults() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');

    Object.entries(this.testResults).forEach(([key, value]) => {
      console.log(`${value ? '✅' : '❌'} ${key}: ${value}`);
    });

    const allPassed = Object.values(this.testResults).every(v => v);

    console.log('\n' + '='.repeat(50));
    if (allPassed) {
      console.log('🎉 ALL TESTS PASSED! End-to-end flow is working correctly.');
    } else {
      console.log('❌ SOME TESTS FAILED. Check the results above.');
    }
    console.log('='.repeat(50));

    return allPassed;
  }

  async runFullTest() {
    console.log('🚀 Starting Remote End-to-End Test');
    console.log('===================================');
    console.log(`🌐 Target system: ${CONFIG.API_BASE}`);
    console.log(`📧 Test domain: ${CONFIG.TEST_DOMAIN}`);
    console.log(`⏰ Started at: ${new Date().toISOString()}\n`);

    let success = false;

    try {
      // Step 1: Check system health
      await this.checkSystemHealth();

      // Step 2: Setup test domain and webhook
      if (!await this.setupTestDomain()) {
        throw new Error('Failed to setup test domain');
      }

      // Step 3: Send test email
      const emailResult = await this.sendTestEmail();
      if (!emailResult) {
        throw new Error('Failed to send test email');
      }

      // Step 4: Wait for webhook delivery
      const webhookResult = await this.waitForWebhookDelivery();

      // Step 5: Validate payload
      this.validatePayload(webhookResult.payload);

      // Step 6: Print results
      success = this.printTestResults();

    } catch (error) {
      console.error('\n💥 Test failed with error:', error.message);
      this.printTestResults();
    } finally {
      // Always cleanup
      await this.cleanup();
    }

    process.exit(success ? 0 : 1);
  }
}

// Run the test if called directly
if (require.main === module) {
  const testRunner = new RemoteE2ETestRunner();
  testRunner.runFullTest().catch(console.error);
}

module.exports = { RemoteE2ETestRunner, WebhookSiteManager, EmailSender, CONFIG };
