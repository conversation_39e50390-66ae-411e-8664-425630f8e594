import { createRouter, createWebHistory } from 'vue-router'
import DomainsView from './components/dashboard/DomainsView.vue'
import AliasesView from './components/dashboard/AliasesView.vue'
import WebhooksView from './components/dashboard/WebhooksView.vue'
import LogsView from './components/dashboard/LogsView.vue'
import SettingsPage from './components/settings/SettingsPage.vue'
import LoginPage from './components/auth/LoginPage.vue'
import RegisterPage from './components/auth/RegisterPage.vue'
import LandingPage from './components/landing/LandingPage.vue'

const routes = [
  // Landing page (public)
  {
    path: '/',
    name: 'home',
    component: LandingPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Auth routes (public)
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  // Dashboard routes (protected) - clean URLs
  {
    path: '/dashboard',
    redirect: '/domains'
  },
  {
    path: '/domains',
    name: 'domains',
    component: DomainsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/aliases',
    name: 'aliases',
    component: AliasesView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/webhooks',
    name: 'webhooks',
    component: WebhooksView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/logs',
    name: 'logs',
    component: LogsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/settings',
    name: 'settings',
    component: SettingsPage,
    meta: { requiresAuth: true, layout: 'user' }
  },
] as const

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
